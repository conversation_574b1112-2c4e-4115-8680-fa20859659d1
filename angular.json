{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"lib": {"projectType": "library", "root": "projects/lib", "sourceRoot": "projects/lib/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"tsConfig": "projects/lib/tsconfig.lib.json", "project": "projects/lib/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/lib/tsconfig.lib.prod.json"}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/lib/src/test.ts", "tsConfig": "projects/lib/tsconfig.spec.json", "karmaConfig": "projects/lib/karma.conf.js"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["projects/lib/tsconfig.lib.json", "projects/lib/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "storybook": {"root": "", "projectType": "application", "architect": {"build": {"builder": "@storybook/angular:start-storybook", "options": {"tsConfig": "projects/lib/tsconfig.lib.json", "styles": ["projects/lib/src/styles/themes/hub-common-theme.scss"]}}}}}, "cli": {"analytics": false}, "defaultProject": "lib"}