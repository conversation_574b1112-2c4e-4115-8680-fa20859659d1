{"name": "app", "version": "0.1.0", "scripts": {"ng": "ng", "start:storybook": "start-storybook", "prebuild": "rimraf ./dist", "build": "npm-run-all --parallel build:*", "build:lib": "ng build lib --configuration production && npm run build-assets", "build:storybook": "build-storybook -o ./dist/storybook", "build-assets": "npm-run-all --parallel build-assets:*", "build-assets:fonts-copy": "cpx \"./projects/lib/src/styles/fonts/**/*.{eot,svg,ttf,woff,woff2}\" ./dist/lib/styles/fonts/", "build-assets:fonts-scss": "scss-bundle -c ./projects/lib/config/scss-bundle/fonts.config.json", "build-assets:scss-common-theme": "scss-bundle -c ./projects/lib/config/scss-bundle/theme.config.json", "test": "npm-run-all --parallel test:*", "test:lib": "ng test lib", "test:file": "ng test lib", "lint": "npm-run-all --parallel lint:*", "lint:lib": "ng lint lib", "pack": "npm run build:lib && cd ./dist/lib && npm pack && cd ../.."}, "private": true, "dependencies": {"@angular/animations": "12.2.5", "@angular/cdk": "12.2.5", "@angular/common": "12.2.5", "@angular/compiler": "12.2.5", "@angular/core": "12.2.5", "@angular/flex-layout": "12.0.0-beta.34", "@angular/forms": "12.2.5", "@angular/material": "12.2.5", "@angular/platform-browser": "12.2.5", "@angular/platform-browser-dynamic": "12.2.5", "@angular/router": "12.2.5", "@auth0/angular-jwt": "5.0.2", "rxjs": "6.6.3", "tslib": "2.1.0", "zone.js": "0.11.4", "dexie": "3.0.2"}, "devDependencies": {"@angular-devkit/build-angular": "12.2.5", "@angular/cli": "12.2.5", "@angular/compiler-cli": "12.2.5", "@angular/language-service": "12.2.5", "@babel/core": "7.12.9", "@ngneat/spectator": "6.1.2", "@ngx-translate/core": "13.0.0", "@storybook/addon-actions": "6.4.1", "@storybook/addon-cssresources": "6.2.9", "@storybook/angular": "6.4.1", "@storybook/builder-webpack5": "6.4.1", "@storybook/manager-webpack5": "6.4.1", "@types/crypto-js": "4.0.1", "@types/jasmine": "3.6.2", "@types/jasminewd2": "2.0.8", "@types/lodash": "4.14.119", "@types/node": "12.0.8", "@types/tapable": "2.2.2", "autoprefixer": "10.0.4", "babel-loader": "8.2.2", "codelyzer": "6.0.1", "cpx": "1.5.0", "crypto-js": "4.0.0", "jasmine-core": "3.6.0", "jasmine-spec-reporter": "6.0.0", "karma": "6.3.4", "karma-chrome-launcher": "3.1.0", "karma-coverage-istanbul-reporter": "3.0.3", "karma-jasmine": "4.0.1", "karma-jasmine-html-reporter": "1.5.4", "moment": "2.29.1", "moment-timezone": "0.5.32", "ng-packagr": "12.2.1", "ngx-color-picker": "10.1.0", "npm-run-all": "4.1.5", "rimraf": "3.0.2", "rxjs-tslint": "0.1.8", "sass": "1.41.0", "scss-bundle": "3.1.2", "ts-node": "9.0.0", "tsickle": "0.43.0", "tslint": "6.1.3", "typescript": "4.3.5"}, "browserslist": [">0.2%", "not dead", "not ie <= 9"]}